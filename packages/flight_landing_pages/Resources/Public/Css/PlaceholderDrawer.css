/**
 * Flight Landing Pages - Placeholder Drawer Styles
 */

/* Drawer container */
.placeholder-drawer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--typo3-component-bg, #fff);
    border-left: 1px solid var(--typo3-component-border-color, #d4d4d4);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    transition: right 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    font-family: var(--typo3-font-family-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
}

.placeholder-drawer.open {
    right: 0;
}

/* Drawer header */
.placeholder-drawer-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--typo3-component-border-color, #d4d4d4);
    background: var(--typo3-surface-container-low, #f8f9fa);
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.placeholder-drawer-header button {
    margin-left: auto;
    flex-shrink: 0;
}

/* Drawer content */
.placeholder-drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Search section */
.placeholder-search {
    flex: 1;
}

.placeholder-search input {
    width: 100%;
    border: 1px solid var(--typo3-component-border-color, #d4d4d4);
    border-radius: var(--typo3-component-border-radius, 4px);
    font-size: 13px;
    background: var(--typo3-component-bg, #fff);
    color: var(--typo3-text-color-base, #212529);
}

.placeholder-search input:focus {
    outline: none;
    border-color: var(--typo3-component-primary-color, #007cba);
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Placeholder list */
.placeholder-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

/* Placeholder item */
.placeholder-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--typo3-surface-container-low, #f1f1f1);
    display: flex;
    align-items: flex-start;
    gap: 12px;
    transition: background-color 0.2s ease;
}

.placeholder-item:hover {
    background-color: var(--typo3-surface-container-low, #f8f9fa);
}

.placeholder-item:last-child {
    border-bottom: none;
}

.placeholder-content {
    flex: 1;
    min-width: 0;
}

/* Placeholder code */
.placeholder-code {
    font-family: var(--typo3-font-family-monospace, 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace);
    font-size: 12px;
    color: var(--typo3-text-color-variant, #d73a49);
    background: var(--typo3-surface-container-low, #f6f8fa);
    padding: 3px 6px;
    border-radius: var(--typo3-component-border-radius, 3px);
    margin-bottom: 4px;
    display: inline-block;
    font-weight: 500;
    word-break: break-all;
}

/* Placeholder description */
.placeholder-description {
    font-size: 12px;
    color: var(--typo3-text-color-muted, #6c757d);
    line-height: 1.4;
}

/* Copy button */
.placeholder-copy {
    flex-shrink: 0;
    padding: 4px;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
    background: transparent;
    color: var(--typo3-text-color-muted, #6c757d);
    border-radius: var(--typo3-component-border-radius, 3px);
    cursor: pointer;
    transition: all 0.2s ease;
}

.placeholder-copy:hover {
    background: var(--typo3-component-primary-color, #007cba);
    color: #fff;
    border-color: var(--typo3-component-primary-color, #007cba);
}

.placeholder-copy .icon-markup {
    font-size: 14px;
}

/* No results message */
.placeholder-no-results {
    padding: 20px;
    text-align: center;
    color: var(--typo3-text-color-muted, #6c757d);
    font-style: italic;
    font-size: 13px;
}

/* Toggle button active state */
#placeholder-drawer-toggle.active {
    background-color: var(--typo3-component-primary-color, #007cba);
    color: #fff;
    border-color: var(--typo3-component-primary-color, #007cba);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .placeholder-drawer {
        width: 350px;
        right: -350px;
    }
}

@media (max-width: 768px) {
    .placeholder-drawer {
        width: 100%;
        right: -100%;
    }
}

/* Scrollbar styling for webkit browsers */
.placeholder-list::-webkit-scrollbar {
    width: 6px;
}

.placeholder-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.placeholder-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.placeholder-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for smooth transitions */
.placeholder-item {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
