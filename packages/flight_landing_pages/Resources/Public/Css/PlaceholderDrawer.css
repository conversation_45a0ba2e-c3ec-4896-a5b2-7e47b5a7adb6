/**
 * Flight Landing Pages - Placeholder Drawer Styles
 */

/* Drawer container */
.placeholder-drawer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: #fff;
    border-left: 1px solid #d4d4d4;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    transition: right 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
}

.placeholder-drawer.open {
    right: 0;
}

/* Drawer header */
.placeholder-drawer-header {
    padding: 15px 20px;
    border-bottom: 1px solid #d4d4d4;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.placeholder-drawer-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.placeholder-drawer-header button {
    padding: 4px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
}

.placeholder-drawer-header button:hover {
    background: #e9ecef;
}

/* Drawer content */
.placeholder-drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Search section */
.placeholder-search {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;
}

.placeholder-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d4d4d4;
    border-radius: 4px;
    font-size: 14px;
}

.placeholder-search input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Placeholder list */
.placeholder-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

/* Placeholder item */
.placeholder-item {
    padding: 12px 20px;
    border-bottom: 1px solid #f1f1f1;
    position: relative;
    transition: background-color 0.2s ease;
}

.placeholder-item:hover {
    background-color: #f8f9fa;
}

.placeholder-item:last-child {
    border-bottom: none;
}

/* Placeholder code */
.placeholder-code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #d73a49;
    background: #f6f8fa;
    padding: 4px 8px;
    border-radius: 3px;
    margin-bottom: 6px;
    display: inline-block;
    font-weight: 600;
}

/* Placeholder description */
.placeholder-description {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

/* Copy button */
.placeholder-copy {
    position: absolute;
    top: 12px;
    right: 20px;
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid #d4d4d4;
    background: #fff;
    color: #333;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.placeholder-copy:hover {
    background: #007cba;
    color: #fff;
    border-color: #007cba;
}

/* No results message */
.placeholder-no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Toggle button active state */
#placeholder-drawer-toggle.active {
    background-color: #007cba;
    color: #fff;
    border-color: #007cba;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .placeholder-drawer {
        width: 350px;
        right: -350px;
    }
}

@media (max-width: 768px) {
    .placeholder-drawer {
        width: 100%;
        right: -100%;
    }
}

/* Scrollbar styling for webkit browsers */
.placeholder-list::-webkit-scrollbar {
    width: 6px;
}

.placeholder-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.placeholder-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.placeholder-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for smooth transitions */
.placeholder-item {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
