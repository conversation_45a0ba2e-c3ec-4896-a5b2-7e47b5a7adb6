/**
 * Flight Landing Pages - Placeholder Drawer
 *
 * Provides a right-side drawer with available placeholders for template pages
 */
define(['jquery'], function($) {
    'use strict';

    /**
     * PlaceholderDrawer object
     */
    var PlaceholderDrawer = {
        isInitialized: false,
        isOpen: false,
        drawer: null,
        searchInput: null,
        placeholderList: null,

        /**
         * Available placeholders with descriptions
         */
        placeholders: [
            // Origin placeholders
            {
                placeholder: '[_origin_code_]',
                description: 'Origin airport code (e.g., IST)',
                category: 'origin'
            },
            {
                placeholder: '[_origin_name_]',
                description: 'Origin airport/city name (e.g., Istanbul)',
                category: 'origin'
            },
            {
                placeholder: '[_origin_type_]',
                description: 'Origin type (airport/city/country)',
                category: 'origin'
            },
            // Destination placeholders
            {
                placeholder: '[_destination_code_]',
                description: 'Destination airport code (e.g., VAR)',
                category: 'destination'
            },
            {
                placeholder: '[_destination_name_]',
                description: 'Destination airport/city name (e.g., Varna)',
                category: 'destination'
            },
            {
                placeholder: '[_destination_type_]',
                description: 'Destination type (airport/city/country)',
                category: 'destination'
            },
            // Route placeholders
            {
                placeholder: '[_route_slug_]',
                description: 'Route slug (e.g., ist-var)',
                category: 'route'
            },
            {
                placeholder: '[_is_active_]',
                description: 'Route status (active or inactive)',
                category: 'route'
            },
            // Combined placeholders
            {
                placeholder: '[_route_]',
                description: 'Full route with arrow (e.g., IST → VAR)',
                category: 'combined'
            },
            {
                placeholder: '[_route_dash_]',
                description: 'Route with dash (e.g., IST-VAR)',
                category: 'combined'
            },
            {
                placeholder: '[_route_text_]',
                description: 'Route in text format (e.g., Istanbul to Varna)',
                category: 'combined'
            }
        ],

        /**
         * Initialize the placeholder drawer
         */
        initialize: function() {
            if (this.isInitialized) {
                return;
            }

            // Check if we're on a template page
            if (!this.isTemplatePage()) {
                return;
            }

            this.createDrawer();
            this.createToggleButton();
            this.bindEvents();
            this.isInitialized = true;
        },

        /**
         * Check if current page is a template page
         */
        isTemplatePage: function() {
            // Check if we're editing content elements
            var isContentEdit = window.location.href.includes('record/edit') &&
                               window.location.href.includes('tt_content');

            if (!isContentEdit) {
                return false;
            }

            // Extract content ID from URL parameters
            var contentId = this.getContentIdFromUrl();
            if (!contentId) {
                return false;
            }

            // Check if the content element belongs to a template page via AJAX
            return this.checkIfTemplatePageAsync(contentId);
        },

        /**
         * Extract content ID from current URL
         */
        getContentIdFromUrl: function() {
            var urlParams = new URLSearchParams(window.location.search);

            // Try to get content ID from edit parameters
            var editParam = urlParams.get('edit');
            if (editParam) {
                // Parse edit parameter like "tt_content[123]" to get the content ID
                var match = editParam.match(/tt_content\[(\d+)\]/);
                if (match) {
                    return match[1];
                }
            }

            return null;
        },

        /**
         * Check if content element belongs to a template page via AJAX
         */
        checkIfTemplatePageAsync: function(contentId) {
            var isTemplatePage = false;

            // Synchronous AJAX call to check if content belongs to template page
            $.ajax({
                url: TYPO3.settings.ajaxUrls.flight_landing_pages_check_template_page,
                method: 'GET',
                async: false,
                data: {
                    contentId: contentId
                },
                success: function(response) {
                    if (response && response.isTemplatePage === true) {
                        isTemplatePage = true;
                    }
                },
                error: function() {
                    // If AJAX fails, don't show the drawer
                    isTemplatePage = false;
                }
            });

            return isTemplatePage;
        },

        /**
         * Create the drawer HTML structure
         */
        createDrawer: function() {
            var drawerHtml = `
                <div id="placeholder-drawer" class="placeholder-drawer">
                    <div class="placeholder-drawer-header">
                        <div class="placeholder-search">
                            <input type="text" class="form-control form-control-sm" id="placeholder-search" placeholder="Search placeholders...">
                        </div>
                        <button type="button" class="btn btn-sm btn-borderless" id="placeholder-drawer-close" title="Close">
                            <span class="t3js-icon icon icon-size-small icon-state-default icon-actions-close" data-identifier="actions-close">
                                <span class="icon-markup">×</span>
                            </span>
                        </button>
                    </div>
                    <div class="placeholder-drawer-content">
                        <div class="placeholder-list" id="placeholder-list">
                            <!-- Placeholders will be rendered here -->
                        </div>
                    </div>
                </div>
            `;

            $('body').append(drawerHtml);
            this.drawer = $('#placeholder-drawer');
            this.searchInput = $('#placeholder-search');
            this.placeholderList = $('#placeholder-list');

            this.renderPlaceholders();
        },

        /**
         * Create toggle button
         */
        createToggleButton: function() {
            var buttonHtml = `
                <button type="button" class="btn btn-default" id="placeholder-drawer-toggle" title="Show Flight Placeholders">
                    <span class="t3js-icon icon icon-size-small icon-state-default icon-content-text-columns" data-identifier="content-text-columns">
                        <span class="icon-markup">{}</span>
                    </span>
                    Placeholders
                </button>
            `;

            // Add button to the toolbar
            $('.module-docheader-bar .btn-toolbar').first().append(buttonHtml);
        },

        /**
         * Render placeholders in the list
         */
        renderPlaceholders: function(filter = '') {
            var html = '';
            var filteredPlaceholders = this.placeholders.filter(function(item) {
                return filter === '' || 
                       item.placeholder.toLowerCase().includes(filter.toLowerCase()) ||
                       item.description.toLowerCase().includes(filter.toLowerCase());
            });

            filteredPlaceholders.forEach(function(item) {
                html += `
                    <div class="placeholder-item" data-placeholder="${item.placeholder}">
                        <div class="placeholder-content">
                            <div class="placeholder-code">${item.placeholder}</div>
                            <div class="placeholder-description">${item.description}</div>
                        </div>
                        <button type="button" class="btn btn-sm btn-borderless placeholder-copy" data-placeholder="${item.placeholder}" title="Copy ${item.placeholder}">
                            <span class="t3js-icon icon icon-size-small icon-state-default icon-actions-document-duplicates" data-identifier="actions-document-duplicates">
                                <span class="icon-markup">📋</span>
                            </span>
                        </button>
                    </div>
                `;
            });

            if (filteredPlaceholders.length === 0) {
                html = '<div class="placeholder-no-results">No placeholders found</div>';
            }

            this.placeholderList.html(html);
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;

            // Toggle drawer
            $(document).on('click', '#placeholder-drawer-toggle', function() {
                self.toggleDrawer();
            });

            // Close drawer
            $(document).on('click', '#placeholder-drawer-close', function() {
                self.closeDrawer();
            });

            // Search functionality
            this.searchInput.on('input', function() {
                self.renderPlaceholders($(this).val());
            });

            // Copy placeholder
            $(document).on('click', '.placeholder-copy', function() {
                var placeholder = $(this).data('placeholder');
                self.copyToClipboard(placeholder);
            });

            // Close drawer when clicking outside
            $(document).on('click', function(e) {
                if (self.isOpen && !$(e.target).closest('#placeholder-drawer, #placeholder-drawer-toggle').length) {
                    self.closeDrawer();
                }
            });
        },

        /**
         * Toggle drawer open/close
         */
        toggleDrawer: function() {
            if (this.isOpen) {
                this.closeDrawer();
            } else {
                this.openDrawer();
            }
        },

        /**
         * Open drawer
         */
        openDrawer: function() {
            this.drawer.addClass('open');
            this.isOpen = true;
            $('#placeholder-drawer-toggle').addClass('active');
        },

        /**
         * Close drawer
         */
        closeDrawer: function() {
            this.drawer.removeClass('open');
            this.isOpen = false;
            $('#placeholder-drawer-toggle').removeClass('active');
        },

        /**
         * Copy text to clipboard
         */
        copyToClipboard: function(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    // Show success feedback
                    top.TYPO3.Notification.success('Copied!', text + ' copied to clipboard');
                });
            } else {
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                top.TYPO3.Notification.success('Copied!', text + ' copied to clipboard');
            }
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        PlaceholderDrawer.initialize();
    });

    return PlaceholderDrawer;
});
