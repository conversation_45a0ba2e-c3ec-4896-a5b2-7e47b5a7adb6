<?php
namespace Bgs\FlightLandingPages\Service;

/**
 * Service for rendering virtual route pages
 * 
 * This service handles the rendering of virtual route pages with template content
 * and flight data, providing a clean separation from the middleware logic.
 */
class VirtualRouteRenderingService
{
    /**
     * Render virtual page using template page data and content
     * This method renders content from the template page without custom HTML structure
     */
    public function renderVirtualPage(array $virtualContent, array $virtualRouteMatch, $controller = null): string
    {
        // For now, always use content-only rendering to avoid TypoScript issues
        // TODO: Implement proper TypoScript integration when lib.dynamicContent is available
        return $this->renderContentOnly($virtualContent, $virtualRouteMatch, $controller);
    }

    /**
     * Render content using template page data for title, SEO, and content
     * This renders a complete HTML page using template page configuration
     */
    protected function renderContentOnly(array $virtualContent, array $virtualRouteMatch, $controller = null): string
    {
        // Get template page data from controller if available
        $pageData = $controller && isset($controller->page) ? $controller->page : [];
        $flightRoute = $virtualRouteMatch['flightRoute'] ?? [];

        // Build page title from template page data
        $title = $pageData['title'] ?? 'Flight Route';
        $description = $pageData['description'] ?? '';
        $keywords = $pageData['keywords'] ?? '';

        // Build complete HTML page using template page configuration
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . '</title>';

        if (!empty($description)) {
            $html .= '
    <meta name="description" content="' . htmlspecialchars($description) . '">';
        }

        if (!empty($keywords)) {
            $html .= '
    <meta name="keywords" content="' . htmlspecialchars($keywords) . '">';
        }

        // Add SEO meta tags from template page
        if (!empty($pageData['seo_title'])) {
            $html .= '
    <meta property="og:title" content="' . htmlspecialchars($pageData['seo_title']) . '">';
        }

        if (!empty($pageData['og_description'])) {
            $html .= '
    <meta property="og:description" content="' . htmlspecialchars($pageData['og_description']) . '">';
        }

        $html .= '
</head>
<body>';

        // Render the content elements from template page
        if (!empty($virtualContent)) {
            foreach ($virtualContent as $contentElement) {
                $html .= '<div class="content-element">';

                if (!empty($contentElement['header'])) {
                    $html .= '<h2>' . htmlspecialchars($contentElement['header']) . '</h2>';
                }

                if (!empty($contentElement['subheader'])) {
                    $html .= '<h3>' . htmlspecialchars($contentElement['subheader']) . '</h3>';
                }

                if (!empty($contentElement['bodytext'])) {
                    $html .= '<div>' . nl2br(htmlspecialchars($contentElement['bodytext'])) . '</div>';
                }

                $html .= '</div>';
            }
        } else {
            $html .= '<p>Test content</p>';
        }

        $html .= '
</body>
</html>';

        return $html;
    }

    /**
     * Render a more advanced virtual page using TYPO3 templates (future enhancement)
     */
    public function renderWithTypoScript(array $virtualContent, array $virtualRouteMatch, $controller): string
    {
        // This method can be implemented later to integrate with TYPO3's template system
        // when the TypoScript issues are resolved
        return $this->renderVirtualPage($virtualContent, $virtualRouteMatch);
    }

    /**
     * Generate page metadata for virtual routes
     */
    public function generatePageMetadata(array $virtualRouteMatch): array
    {
        $flightRoute = $virtualRouteMatch['flightRoute'] ?? [];
        $landingPage = $virtualRouteMatch['landingPage'] ?? [];
        
        $originCode = $flightRoute['origin_code'] ?? '';
        $destinationCode = $flightRoute['destination_code'] ?? '';
        $originName = $flightRoute['origin_name'] ?? $originCode;
        $destinationName = $flightRoute['destination_name'] ?? $destinationCode;
        
        return [
            'title' => "Flight from {$originName} to {$destinationName}",
            'description' => "Find flights from {$originName} ({$originCode}) to {$destinationName} ({$destinationCode})",
            'keywords' => "flights, {$originCode}, {$destinationCode}, {$originName}, {$destinationName}",
            'canonical' => $virtualRouteMatch['requestedPath'] ?? '',
            'og_title' => "Flight Route: {$originCode} → {$destinationCode}",
            'og_description' => "Book your flight from {$originName} to {$destinationName}",
        ];
    }
}
