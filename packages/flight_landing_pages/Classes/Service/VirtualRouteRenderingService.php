<?php
namespace Bgs\FlightLandingPages\Service;

/**
 * Service for rendering virtual route pages
 * 
 * This service handles the rendering of virtual route pages with template content
 * and flight data, providing a clean separation from the middleware logic.
 */
class VirtualRouteRenderingService
{
    /**
     * Render a simple virtual page with processed content
     */
    public function renderVirtualPage(array $virtualContent, array $virtualRouteMatch): string
    {
        $flightRoute = $virtualRouteMatch['flightRoute'] ?? [];
        $originCode = $flightRoute['origin_code'] ?? '';
        $destinationCode = $flightRoute['destination_code'] ?? '';
        
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight from ' . htmlspecialchars($originCode) . ' to ' . htmlspecialchars($destinationCode) . '</title>
</head>
<body>
    <h1>Flight Route: ' . htmlspecialchars($originCode) . ' → ' . htmlspecialchars($destinationCode) . '</h1>';
        
        // Render the virtual content elements
        if (!empty($virtualContent)) {
            foreach ($virtualContent as $contentElement) {
                $html .= '<div class="content-element">';
                
                if (!empty($contentElement['header'])) {
                    $html .= '<h2>' . htmlspecialchars($contentElement['header']) . '</h2>';
                }
                
                if (!empty($contentElement['subheader'])) {
                    $html .= '<h3>' . htmlspecialchars($contentElement['subheader']) . '</h3>';
                }
                
                if (!empty($contentElement['bodytext'])) {
                    $html .= '<div>' . nl2br(htmlspecialchars($contentElement['bodytext'])) . '</div>';
                }
                
                $html .= '</div>';
            }
        } else {
            $html .= '<p>Test content</p>';
        }
        
        $html .= '
</body>
</html>';
        
        return $html;
    }

    /**
     * Render a more advanced virtual page using TYPO3 templates (future enhancement)
     */
    public function renderWithTypoScript(array $virtualContent, array $virtualRouteMatch, $controller): string
    {
        // This method can be implemented later to integrate with TYPO3's template system
        // when the TypoScript issues are resolved
        return $this->renderVirtualPage($virtualContent, $virtualRouteMatch);
    }

    /**
     * Generate page metadata for virtual routes
     */
    public function generatePageMetadata(array $virtualRouteMatch): array
    {
        $flightRoute = $virtualRouteMatch['flightRoute'] ?? [];
        $landingPage = $virtualRouteMatch['landingPage'] ?? [];
        
        $originCode = $flightRoute['origin_code'] ?? '';
        $destinationCode = $flightRoute['destination_code'] ?? '';
        $originName = $flightRoute['origin_name'] ?? $originCode;
        $destinationName = $flightRoute['destination_name'] ?? $destinationCode;
        
        return [
            'title' => "Flight from {$originName} to {$destinationName}",
            'description' => "Find flights from {$originName} ({$originCode}) to {$destinationName} ({$destinationCode})",
            'keywords' => "flights, {$originCode}, {$destinationCode}, {$originName}, {$destinationName}",
            'canonical' => $virtualRouteMatch['requestedPath'] ?? '',
            'og_title' => "Flight Route: {$originCode} → {$destinationCode}",
            'og_description' => "Book your flight from {$originName} to {$destinationName}",
        ];
    }
}
