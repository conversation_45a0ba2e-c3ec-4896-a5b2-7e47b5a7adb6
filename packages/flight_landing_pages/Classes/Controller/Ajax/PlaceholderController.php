<?php
namespace Bgs\FlightLandingPages\Controller\Ajax;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\JsonResponse;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * AJAX Controller for placeholder drawer functionality
 */
class PlaceholderController
{
    /**
     * Check if a content element belongs to a template page
     */
    public function checkTemplatePage(ServerRequestInterface $request): ResponseInterface
    {
        $queryParams = $request->getQueryParams();
        $contentId = (int)($queryParams['contentId'] ?? 0);

        if ($contentId <= 0) {
            return new JsonResponse(['isTemplatePage' => false]);
        }

        try {
            // Get the page ID from the content element
            $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
            $queryBuilder = $connectionPool->getQueryBuilderForTable('tt_content');

            $contentRecord = $queryBuilder
                ->select('pid')
                ->from('tt_content')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($contentId, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$contentRecord) {
                return new JsonResponse(['isTemplatePage' => false]);
            }

            $pageId = (int)$contentRecord['pid'];

            // Check if the page is a template page (doktype = 200)
            $queryBuilder = $connectionPool->getQueryBuilderForTable('pages');

            $pageRecord = $queryBuilder
                ->select('doktype')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAssociative();

            $isTemplatePage = $pageRecord && (int)$pageRecord['doktype'] === 200;

            return new JsonResponse(['isTemplatePage' => $isTemplatePage]);

        } catch (\Exception $e) {
            return new JsonResponse(['isTemplatePage' => false, 'error' => $e->getMessage()]);
        }
    }
}
