<?php
namespace Bgs\FlightLandingPages\Controller\Ajax;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\JsonResponse;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * AJAX Controller for placeholder drawer functionality
 */
class PlaceholderController
{
    /**
     * Check if a content element belongs to a template page
     */
    public function checkTemplatePage(ServerRequestInterface $request): ResponseInterface
    {
        $queryParams = $request->getQueryParams();
        $contentId = (int)($queryParams['contentId'] ?? 0);

        error_log('PlaceholderController: Received contentId: ' . $contentId);

        if ($contentId <= 0) {
            error_log('PlaceholderController: Invalid content ID');
            return new JsonResponse(['isTemplatePage' => false, 'debug' => 'Invalid content ID']);
        }

        try {
            // Get the page ID from the content element
            $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
            $queryBuilder = $connectionPool->getQueryBuilderForTable('tt_content');

            $contentRecord = $queryBuilder
                ->select('pid')
                ->from('tt_content')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($contentId, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$contentRecord) {
                error_log('PlaceholderController: Content record not found');
                return new JsonResponse(['isTemplatePage' => false, 'debug' => 'Content record not found']);
            }

            $pageId = (int)$contentRecord['pid'];
            error_log('PlaceholderController: Found page ID: ' . $pageId);

            // Check if the page is a template page (doktype = 200)
            $queryBuilder = $connectionPool->getQueryBuilderForTable('pages');

            $pageRecord = $queryBuilder
                ->select('doktype', 'title')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT))
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$pageRecord) {
                error_log('PlaceholderController: Page record not found');
                return new JsonResponse(['isTemplatePage' => false, 'debug' => 'Page record not found']);
            }

            $doktype = (int)$pageRecord['doktype'];
            error_log('PlaceholderController: Page doktype: ' . $doktype . ', title: ' . $pageRecord['title']);

            $isTemplatePage = $doktype === 200;
            error_log('PlaceholderController: Is template page: ' . ($isTemplatePage ? 'true' : 'false'));

            return new JsonResponse([
                'isTemplatePage' => $isTemplatePage,
                'debug' => [
                    'contentId' => $contentId,
                    'pageId' => $pageId,
                    'doktype' => $doktype,
                    'pageTitle' => $pageRecord['title']
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse(['isTemplatePage' => false, 'error' => $e->getMessage()]);
        }
    }
}
