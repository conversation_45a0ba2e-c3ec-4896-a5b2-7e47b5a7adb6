<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register backend module if needed
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr(
        'tx_flightlandingpages_domain_model_flightroute',
        'EXT:flight_landing_pages/Resources/Private/Language/locallang_csh_tx_flightlandingpages_domain_model_flightroute.xlf'
    );
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_flightlandingpages_domain_model_flightroute');

    // Add backend assets for placeholder drawer via PageRenderer hook
    $GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['t3lib/class.t3lib_pagerenderer.php']['render-preProcess'][] =
        function($params, $pageRenderer) {
            if (strpos($_SERVER['REQUEST_URI'] ?? '', '/typo3/') !== false) {
                $pageRenderer->addCssFile(
                    'EXT:flight_landing_pages/Resources/Public/Css/PlaceholderDrawer.css'
                );
                $pageRenderer->loadRequireJsModule(
                    'TYPO3/CMS/FlightLandingPages/PlaceholderDrawer'
                );
            }
        };
});
