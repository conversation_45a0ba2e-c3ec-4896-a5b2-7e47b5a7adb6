<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register backend module if needed
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr(
        'tx_flightlandingpages_domain_model_flightroute',
        'EXT:flight_landing_pages/Resources/Private/Language/locallang_csh_tx_flightlandingpages_domain_model_flightroute.xlf'
    );
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_flightlandingpages_domain_model_flightroute');

    // Add backend assets for placeholder drawer
    if (\TYPO3\CMS\Core\Core\Environment::isCli() === false) {
        $pageRenderer = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Page\PageRenderer::class);

        // Add CSS for placeholder drawer
        $pageRenderer->addCssFile(
            'EXT:flight_landing_pages/Resources/Public/Css/PlaceholderDrawer.css'
        );

        // Add JavaScript for placeholder drawer
        $pageRenderer->loadRequireJsModule(
            'TYPO3/CMS/FlightLandingPages/PlaceholderDrawer'
        );
    }
});
