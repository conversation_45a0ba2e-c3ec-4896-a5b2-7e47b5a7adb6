<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Configure FlightReference plugin (for content elements)
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
        'FlightLandingPages',
        'FlightReference',
        [
            \Bgs\FlightLandingPages\Controller\FlightReferenceController::class => 'list',
        ],
        // non-cacheable actions
        []
    );



    // Register page type icons
    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-template',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-template.svg']
    );
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-landing',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-landing.svg']
    );

    // Register RequireJS configuration for backend modules
    $GLOBALS['TYPO3_CONF_VARS']['BE']['requireJsConfig']['paths']['TYPO3/CMS/FlightLandingPages/PlaceholderDrawer'] =
        'EXT:flight_landing_pages/Resources/Public/JavaScript/PlaceholderDrawer';

    // Register AJAX endpoint for placeholder drawer
    $GLOBALS['TYPO3_CONF_VARS']['BE']['AJAX']['flight_landing_pages_check_template_page'] =
        \Bgs\FlightLandingPages\Controller\Ajax\PlaceholderController::class . '::checkTemplatePage';
});
